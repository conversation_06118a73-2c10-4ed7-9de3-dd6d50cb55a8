# ============================================================================
# DOAXVV HANDBOOK BACKEND CONFIGURATION
# ============================================================================
# Copy this file to .env and configure your environment variables

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# ============================================================================
# DATABASE CONFIGURATION (MySQL)
# ============================================================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=doaxvv_handbook
DB_USER=doaxvv_user
DB_PASSWORD=doaxvv_password

# Database Connection Pool Settings
DB_CONNECTION_LIMIT=20
DB_ACQUIRE_TIMEOUT=15000
DB_TIMEOUT=30000
DB_MAX_CONNECTION_ATTEMPTS=5
DB_CONNECTION_RETRY_DELAY=5000
DB_MONITORING_INTERVAL=60000

# Database SSL Configuration (Production Only)
DB_SSL_REJECT_UNAUTHORIZED=false
# DB_SSL_CA=path/to/ca-cert.pem
# DB_SSL_CERT=path/to/client-cert.pem
# DB_SSL_KEY=path/to/client-key.pem

# Database Timezone
DB_TIMEZONE=+00:00

# ============================================================================
# CORS CONFIGURATION
# ============================================================================
# Comma-separated list of allowed origins
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# ============================================================================
# API CONFIGURATION
# ============================================================================
API_PREFIX=/api
API_VERSION=v1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Request Size Limits
MAX_REQUEST_SIZE=10mb
MAX_JSON_SIZE=1mb
MAX_URL_ENCODED_SIZE=1mb

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
# Secret for JWT signing (generate a strong random string)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
SESSION_MAX_AGE=86400000

# HTTPS Configuration (Production)
FORCE_HTTPS=false
HSTS_MAX_AGE=31536000

# ============================================================================
# FILE UPLOAD CONFIGURATION
# ============================================================================
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5mb
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp,pdf,csv,json

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# Redis Configuration (Optional)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0

# ============================================================================
# EMAIL CONFIGURATION (Optional)
# ============================================================================
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# ============================================================================
# EXTERNAL API CONFIGURATION
# ============================================================================
# DOAXVV API Configuration (if needed)
# DOAXVV_API_BASE_URL=https://api.doaxvv.com
# DOAXVV_API_KEY=your-api-key

# ============================================================================
# MONITORING & HEALTH CHECK CONFIGURATION
# ============================================================================
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# ============================================================================
# BACKUP CONFIGURATION
# ============================================================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_PATH=backups

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================
# Enable detailed error responses in development
DETAILED_ERRORS=true

# Enable API documentation
ENABLE_API_DOCS=true

# Enable request logging in development
ENABLE_REQUEST_LOGGING=true

# Hot reload for development
ENABLE_HOT_RELOAD=true

# ============================================================================
# PRODUCTION OPTIMIZATIONS
# ============================================================================
# Enable gzip compression
ENABLE_COMPRESSION=true

# Enable static file caching
ENABLE_STATIC_CACHE=true
STATIC_CACHE_MAX_AGE=86400000

# Enable database query optimization
ENABLE_QUERY_OPTIMIZATION=true

# ============================================================================
# LOCALIZATION CONFIGURATION
# ============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,jp,cn,tw,kr

# ============================================================================
# FEATURE FLAGS
# ============================================================================
ENABLE_GACHA_FEATURES=true
ENABLE_SHOP_FEATURES=true
ENABLE_UPDATE_LOGS=true
ENABLE_DOCUMENT_MANAGEMENT=true
ENABLE_ADMIN_FEATURES=true

# ============================================================================
# MIGRATION & SEEDING
# ============================================================================
AUTO_MIGRATE=false
AUTO_SEED=false
MIGRATION_LOCK_TIMEOUT=30000 
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.production
.env.staging

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock



# ESLint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# PM2 logs and pids

# File uploads
uploads/
temp/

# Database files (if using SQLite)
*.db
*.sqlite
*.sqlite3

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.backup
*.bak
*.tmp

# SSL certificates (should be managed separately)
ssl/
certs/
*.pem
*.key
*.crt



# Terraform files (if using IaC)
*.tfstate
*.tfstate.*
.terraform/

# Local development files
.local/
.cache/

# Package manager lock files (choose one)
# package-lock.json
# yarn.lock

# TypeScript cache
*.tsbuildinfo


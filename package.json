{"name": "handbook-workspace", "private": true, "version": "1.0.0", "description": "Handbook", "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"bun run dev:backend\" \"bun run dev:frontend\" --names \"backend,frontend\" --prefix-colors \"blue,green\"", "dev:frontend": "cd frontend && bun run dev", "dev:backend": "cd backend && bun run dev", "build:frontend": "cd frontend && bun run build", "build:backend": "cd backend && bun run build", "build:all": "bun run build:backend && bun run build:frontend", "install:frontend": "cd frontend && bun install", "install:backend": "cd backend && bun install", "install:all": "bun run install:backend && bun run install:frontend", "clean:frontend": "cd frontend && bun run clean", "clean:backend": "cd backend && bun run clean", "clean:all": "bun run clean:frontend && bun run clean:backend && rm -rf node_modules", "lint:frontend": "cd frontend && bun run lint", "lint:backend": "cd backend && bun run lint", "lint:all": "bun run lint:frontend && bun run lint:backend"}, "devDependencies": {"concurrently": "^9.2.0"}}